# Generated by Django 3.2.25 on 2025-07-31 17:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
        ('dj_language', '0002_auto_20220120_1344'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImportantNotice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.JSONField(default=dict, verbose_name='title')),
                ('subtitle', models.JSONField(default=dict, verbose_name='subtitle')),
                ('alert_color', models.CharField(choices=[('red', 'Red'), ('yellow', 'Yellow'), ('green', 'Green'), ('blue', 'Blue')], default='blue', max_length=10, verbose_name='alert color')),
                ('target_route', models.CharField(max_length=255, verbose_name='target route')),
                ('status', models.BooleanField(default=True, verbose_name='status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('language', models.ForeignKey(limit_choices_to={'status': True}, on_delete=django.db.models.deletion.CASCADE, to='dj_language.language', verbose_name='language')),
                ('small_image', filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.FILER_IMAGE_MODEL, verbose_name='small image')),
            ],
            options={
                'verbose_name': 'important notice',
                'verbose_name_plural': 'important notices',
                'ordering': ('-created_at',),
            },
        ),
        migrations.AddIndex(
            model_name='importantnotice',
            index=models.Index(fields=['status'], name='important_n_status_69abcd_idx'),
        ),
        migrations.AddIndex(
            model_name='importantnotice',
            index=models.Index(fields=['language'], name='important_n_languag_67d287_idx'),
        ),
    ]
