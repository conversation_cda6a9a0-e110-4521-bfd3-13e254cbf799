# Generated by Django 3.2.25 on 2025-07-31 17:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('important_notice', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='importantnotice',
            name='alert_color',
            field=models.CharField(blank=True, choices=[('#FF0000', 'Red'), ('#FFFF00', 'Yellow'), ('#00FF00', 'Green'), ('#0000FF', 'Blue')], default='#0000FF', max_length=10, null=True, verbose_name='alert color'),
        ),
        migrations.AlterField(
            model_name='importantnotice',
            name='language',
            field=models.ForeignKey(blank=True, limit_choices_to={'status': True}, null=True, on_delete=django.db.models.deletion.CASCADE, to='dj_language.language', verbose_name='language'),
        ),
        migrations.AlterField(
            model_name='importantnotice',
            name='small_image',
            field=models.ImageField(blank=True, null=True, upload_to='important_notice/', verbose_name='small image'),
        ),
        migrations.AlterField(
            model_name='importantnotice',
            name='subtitle',
            field=models.TextField(blank=True, null=True, verbose_name='subtitle'),
        ),
        migrations.AlterField(
            model_name='importantnotice',
            name='target_route',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='target route'),
        ),
        migrations.AlterField(
            model_name='importantnotice',
            name='title',
            field=models.TextField(blank=True, null=True, verbose_name='title'),
        ),
    ]
