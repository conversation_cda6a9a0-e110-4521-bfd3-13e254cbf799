from rest_framework import generics
from rest_framework.permissions import AllowAny
from .models import ImportantNotice
from .serializers import ImportantNoticeSerializer


class ImportantNoticeListView(generics.ListAPIView):
    serializer_class = ImportantNoticeSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        Returns the latest active notice for the specified language
        """
        language_code = self.request.LANGUAGE_CODE
        return ImportantNotice.objects.filter(
            status=True,
            language__code=language_code
        ).order_by('-created_at')