from django.db import models
from django.utils.translation import gettext_lazy as _
from dj_language.models import Language


class ImportantNotice(models.Model):
    class AlertColor(models.TextChoices):
        RED = '#FF0000', _('Red')
        YELLOW = '#FFFF00', _('Yellow')
        GREEN = '#00FF00', _('Green')
        BLUE = '#0000FF', _('Blue')

    title = models.TextField(verbose_name=_('title'), blank=True, null=True)
    subtitle = models.TextField(verbose_name=_('subtitle'), blank=True, null=True)
    alert_color = models.CharField(
        max_length=10,
        choices=AlertColor.choices,
        default=AlertColor.BLUE,
        verbose_name=_('alert color'),
        blank=True,
        null=True
    )
    small_image = models.ImageField(
        upload_to='important_notice/',
        null=True,
        blank=True,
        verbose_name=_('small image')
    )

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    target_route = models.CharField(max_length=255, verbose_name=_('target route'), blank=True, null=True)
    language = models.ForeignKey(
        Language,
        on_delete=models.CASCADE,
        verbose_name=_('language'),
        limit_choices_to={'status': True},
        blank=True,
        null=True
    )
    status = models.BooleanField(default=True, verbose_name=_('status'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))

    class Meta:
        verbose_name = _('important notice')
        verbose_name_plural = _('important notices')
        ordering = ('-created_at',)
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['language']),
        ]

    def __str__(self):
        return self.title if self.title else '-'