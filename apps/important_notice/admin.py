from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from .models import ImportantNotice


@admin.register(ImportantNotice)
class ImportantNoticeAdmin(admin.ModelAdmin):
    list_display = ('get_title', 'language', 'alert_color', 'status', 'created_at')
    list_filter = ('status', 'language', 'alert_color')
    search_fields = ('title', 'subtitle')
    readonly_fields = ('created_at', 'updated_at')
    
    def get_title(self, obj):
        return str(obj)
    get_title.short_description = _('Title')