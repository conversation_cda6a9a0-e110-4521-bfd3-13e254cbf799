from rest_framework import serializers
from .models import ImportantNotice


class ImportantNoticeSerializer(serializers.ModelSerializer):
    alert_color_display = serializers.CharField(source='get_alert_color_display', read_only=True)

    class Meta:
        model = ImportantNotice
        fields = (
            'id',
            'title',
            'subtitle',
            'alert_color',
            'alert_color_display',
            'small_image',
            'target_route',
            'language',
            'status'
        )