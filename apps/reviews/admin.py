from django.contrib import admin
from .models import Review


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = (
        "platform",
        "app_identifier",
        "author_name",
        "rating",
        "title",
        "last_modified_ts",
    )
    list_filter = ("platform", "rating")
    search_fields = (
        "review_id",
        "author_name",
        "title",
        "text",
        "app_identifier",
    )
    readonly_fields = ("raw_data", "created_at", "last_modified_ts")
    ordering = ("-last_modified_ts",) 