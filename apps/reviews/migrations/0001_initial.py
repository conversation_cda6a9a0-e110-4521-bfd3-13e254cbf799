# Generated by Django 3.2.25 on 2025-07-29 13:28

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('google_play', 'Google Play'), ('app_store', 'Apple App Store'), ('whatsapp', 'WhatsApp'), ('telegram', 'Telegram'), ('habib_talk', 'Habib Talk')], max_length=32)),
                ('app_identifier', models.CharField(help_text='Package name, bundle ID یا شناسه کانال/گروه بسته به پلتفرم', max_length=255)),
                ('review_id', models.CharField(max_length=255)),
                ('author_name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('rating', models.PositiveSmallIntegerField(blank=True, null=True)),
                ('title', models.CharField(blank=True, max_length=255)),
                ('text', models.TextField(blank=True)),
                ('device', models.CharField(blank=True, max_length=255)),
                ('last_modified_ts', models.BigIntegerField()),
                ('raw_data', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-last_modified_ts'],
                'unique_together': {('platform', 'review_id')},
            },
        ),
    ]
