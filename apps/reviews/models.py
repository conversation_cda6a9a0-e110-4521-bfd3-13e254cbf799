from django.db import models


class Review(models.Model):
    """Generic review collected from various platforms (Google Play, App Store, ...)."""

    class Platform(models.TextChoices):
        GOOGLE_PLAY = "google_play", "Google Play"
        APP_STORE = "app_store", "Apple App Store"
        WHATSAPP = "whatsapp", "WhatsApp"
        TELEGRAM = "telegram", "Telegram"
        HABIB_TALK = "habib_talk", "Habib Talk"

    platform = models.Char<PERSON>ield(max_length=32, choices=Platform.choices)
    app_identifier = models.Char<PERSON>ield(
        max_length=255,
        help_text="Package name, bundle ID یا شناسه کانال/گروه بسته به پلتفرم",
    )
    review_id = models.Char<PERSON>ield(max_length=255)

    author_name = models.CharField(max_length=255, blank=True, null=True)
    rating = models.PositiveSmallIntegerField(blank=True, null=True)
    title = models.Char<PERSON><PERSON>(max_length=255, blank=True)
    text = models.TextField(blank=True)
    device = models.CharField(max_length=255, blank=True)

    last_modified_ts = models.BigIntegerField()
    raw_data = models.JSONField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-last_modified_ts"]
        unique_together = ("platform", "review_id")

    def __str__(self):
        return f"{self.platform} | {self.review_id} ({self.rating or '-'}★)" 