from __future__ import annotations

"""Celery task to fetch recent Google Play reviews and forward to Telegram.
این ماژول داخل اپ reviews قرار دارد تا همراه مدل‌ها باشد و به‌صورت خودکار توسط Celery کشف شود."""

import os
import time
from datetime import datetime, timezone
from typing import List, Dict, Optional

import requests
from celery import shared_task
from celery.schedules import crontab
from google.oauth2 import service_account
from googleapiclient.discovery import build

from config.celery import app as celery_app
from .models import Review

SCOPES = ["https://www.googleapis.com/auth/androidpublisher"]
DEFAULT_FIRST_RUN_MAX_SEND = int(os.environ.get("FIRST_RUN_MAX_SEND", "50"))

# fallback defaults (can be overridden via env vars)
TELEGRAM_BOT_TOKEN_DEFAULT = os.environ.get("TELEGRAM_BOT_TOKEN_DEFAULT", "")
TELEGRAM_CHAT_ID_DEFAULT = os.environ.get("TELEGRAM_CHAT_ID_DEFAULT", "")
GOOGLE_PLAY_SERVICE_ACCOUNT_JSON_DEFAULT = os.environ.get("GOOGLE_PLAY_SERVICE_ACCOUNT_JSON_DEFAULT", "")
GOOGLE_PLAY_PACKAGE_NAME_DEFAULT = os.environ.get("GOOGLE_PLAY_PACKAGE_NAME_DEFAULT", "")

# ---------------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------------

def _escape_markdown(text: str) -> str:
    escape_chars = "_[]()~`>#+-=|{}.!*"
    return "".join(f"\\{c}" if c in escape_chars else c for c in text)


def _get_telegram_credentials() -> tuple[str, str]:
    bot_token = os.environ.get("TELEGRAM_BOT_TOKEN", TELEGRAM_BOT_TOKEN_DEFAULT)
    chat_id = os.environ.get("TELEGRAM_CHAT_ID", TELEGRAM_CHAT_ID_DEFAULT)
    if not bot_token or not chat_id:
        raise RuntimeError("TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID must be set (env or default).")
    return bot_token, chat_id


def _send_to_telegram(messages: List[str]) -> None:
    bot_token, chat_id = _get_telegram_credentials()
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    for msg in messages:
        try:
            requests.post(url, data={
                "chat_id": chat_id,
                "text": msg,
                "parse_mode": "MarkdownV2",
                "disable_web_page_preview": True,
            }, timeout=10)
        except Exception:
            pass
        time.sleep(1)


def _build_play_service():
    service_account_file = os.environ.get("GOOGLE_PLAY_SERVICE_ACCOUNT_JSON", GOOGLE_PLAY_SERVICE_ACCOUNT_JSON_DEFAULT)
    if not service_account_file or not os.path.exists(service_account_file):
        raise RuntimeError("Service account json path is not provided.")
    credentials = service_account.Credentials.from_service_account_file(service_account_file, scopes=SCOPES)
    return build("androidpublisher", "v3", credentials=credentials, cache_discovery=False)


def _extract_review_timestamp(review: Dict) -> Optional[int]:
    last_modified = review.get("lastModified", {})
    seconds = last_modified.get("seconds") or last_modified.get("secondsEpoch")
    try:
        return int(seconds) if seconds is not None else None
    except (TypeError, ValueError):
        return None


def _format_review_msg(review: Dict) -> str:
    user_comment = review.get("comments", [{}])[0].get("userComment", {})
    star_rating = user_comment.get("starRating", 0)
    rating_str = "⭐" * int(star_rating)
    title = user_comment.get("title", "")
    text = user_comment.get("text", "")
    author_name = user_comment.get("authorName", "User")
    device = user_comment.get("device", "")
    ts = _extract_review_timestamp(review) or int(time.time())
    date_str = datetime.fromtimestamp(ts, tz=timezone.utc).strftime("%Y-%m-%d %H:%M")

    return (
        f"{_escape_markdown(rating_str)}\n"
        f"*{_escape_markdown(title)}*\n"
        f"{_escape_markdown(text)}\n\n"
        f"👤 {_escape_markdown(author_name)} | 📱 {_escape_markdown(device)} | 🗓 {date_str}"
    )

# ---------------------------------------------------------------------------
# Celery task
# ---------------------------------------------------------------------------

@shared_task(name="google_play_fetch_send_reviews", ignore_result=True)
def fetch_and_send_google_play_reviews():
    package_name = os.environ.get("GOOGLE_PLAY_PACKAGE_NAME", GOOGLE_PLAY_PACKAGE_NAME_DEFAULT)
    if not package_name:
        raise RuntimeError("GOOGLE_PLAY_PACKAGE_NAME must be set (env or default)")

    service = _build_play_service()
    last_ts: Optional[int] = (
        Review.objects.filter(platform=Review.Platform.GOOGLE_PLAY, app_identifier=package_name)
        .order_by("-last_modified_ts")
        .values_list("last_modified_ts", flat=True)
        .first()
    )
    first_run = last_ts is None
    first_run_limit = DEFAULT_FIRST_RUN_MAX_SEND

    messages: List[str] = []
    fetched_latest_ts: Optional[int] = None

    request = service.reviews().list(packageName=package_name, maxResults=200)
    stop_paging = False
    while request is not None:
        response = request.execute()
        for review in response.get("reviews", []):
            user_comment = review.get("comments", [{}])[0].get("userComment", {})
            ts = _extract_review_timestamp(review)
            if ts is None:
                continue
            if fetched_latest_ts is None or ts > fetched_latest_ts:
                fetched_latest_ts = ts
            if last_ts and ts <= last_ts:
                stop_paging = True
                break
            obj, created = Review.objects.get_or_create(
                platform=Review.Platform.GOOGLE_PLAY,
                app_identifier=package_name,
                review_id=review.get("reviewId"),
                defaults={
                    "author_name": user_comment.get("authorName", ""),
                    "rating": int(user_comment.get("starRating", 0)),
                    "title": user_comment.get("title", ""),
                    "text": user_comment.get("text", ""),
                    "device": user_comment.get("device", ""),
                    "last_modified_ts": ts,
                    "raw_data": review,
                },
            )

            if created:
                # Only send to Telegram when we insert a brand-new review
                messages.append(_format_review_msg(review))
            else:
                # If review exists but has a newer last_modified_ts, update fields silently
                if ts and ts > obj.last_modified_ts:
                    obj.author_name = user_comment.get("authorName", obj.author_name)
                    obj.rating = int(user_comment.get("starRating", obj.rating or 0))
                    obj.title = user_comment.get("title", obj.title)
                    obj.text = user_comment.get("text", obj.text)
                    obj.device = user_comment.get("device", obj.device)
                    obj.last_modified_ts = ts
                    obj.raw_data = review
                    obj.save(update_fields=[
                        "author_name",
                        "rating",
                        "title",
                        "text",
                        "device",
                        "last_modified_ts",
                        "raw_data",
                    ])
        if first_run and len(messages) >= first_run_limit:
            break
        if stop_paging:
            break
        request = service.reviews().list_next(previous_request=request, previous_response=response)
        if request is not None and first_run and len(messages) >= first_run_limit:
            request = None

    messages.reverse()
    if messages:
        _send_to_telegram(messages)

# Register periodic schedule
@celery_app.on_after_finalize.connect
def _setup_periodic_tasks(sender, **kwargs):
    sender.add_periodic_task(
        crontab(minute=0, hour="*/3", timezone="Asia/Tehran"),
        fetch_and_send_google_play_reviews.s(),
        name="Fetch Google Play reviews and send to Telegram every 3h",
    ) 